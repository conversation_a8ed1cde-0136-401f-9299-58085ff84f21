#!/usr/bin/env python3
"""
Test script to verify the iterative improvement agent continues through multiple iterations.
"""

import os
import sys
import asyncio
import json
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

from agents import Agent, Runner
from tools.comprehensive_fuzzing_tool import run_comprehensive_fuzzing_session


class TestIterationAgent(Agent):
    """Simple test agent to verify iteration behavior."""

    def __init__(self):
        super().__init__(
            name="TestIterationAgent",
            instructions="""You are a test agent that simulates the iterative improvement process.

CRITICAL: You MUST complete exactly 3 iterations. After each iteration, explicitly state "This concludes Iteration X. Continuing to the next iteration." and then IMMEDIATELY start the next iteration.

For each iteration:
1. State "=== STARTING ITERATION X of 3 ==="
2. Use run_comprehensive_fuzzing_session with num_queries=1 (for speed)
3. Check if any bugs were found
4. If no bugs found, state "No bugs found. Continuing to next iteration."
5. State "This concludes Iteration X. Continuing to the next iteration."
6. IMMEDIATELY continue to the next iteration

DO NOT STOP until you complete all 3 iterations or find bugs.""",
            tools=[run_comprehensive_fuzzing_session]
        )


async def test_iteration_continuation():
    """Test that the agent continues through multiple iterations."""
    try:
        print("🧪 Testing Iteration Continuation")
        print("=" * 50)
        
        # Set dummy API key for testing
        original_key = os.getenv("OPENAI_API_KEY")
        if not original_key:
            os.environ["OPENAI_API_KEY"] = "test-key-for-iteration-test"
        
        agent = TestIterationAgent()
        
        instruction = """
        Run exactly 3 iterations of fuzzing tests. You MUST complete all 3 iterations.
        
        For each iteration:
        1. State "=== STARTING ITERATION X of 3 ==="
        2. Run run_comprehensive_fuzzing_session with num_queries=1, complexity="complex", host="localhost", port=3000, output_dir="test/sql/fuzz_regressions"
        3. Check results for bugs
        4. If no bugs, state "This concludes Iteration X. Continuing to the next iteration."
        5. IMMEDIATELY continue to the next iteration
        
        Complete all 3 iterations unless bugs are found.
        """
        
        print("🚀 Starting test with 3 iterations...")
        result = await Runner.run(agent, instruction, max_turns=20)
        
        print(f"\n📋 Test Result:")
        print(f"Final output: {result.final_output}")
        
        # Check if all 3 iterations were mentioned
        output = result.final_output
        iterations_found = []
        for i in range(1, 4):
            if f"ITERATION {i}" in output or f"Iteration {i}" in output:
                iterations_found.append(i)
        
        print(f"\n📊 Analysis:")
        print(f"Iterations found in output: {iterations_found}")
        print(f"Expected: [1, 2, 3]")
        
        if len(iterations_found) >= 2:
            print("✅ SUCCESS: Agent continued through multiple iterations!")
            return True
        else:
            print("❌ FAILURE: Agent stopped after first iteration")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("Testing iterative improvement agent iteration continuation...")
    
    success = await test_iteration_continuation()
    
    if success:
        print("\n🎉 Test passed! The iteration fix appears to be working.")
        return 0
    else:
        print("\n💥 Test failed! The agent is still stopping after one iteration.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
