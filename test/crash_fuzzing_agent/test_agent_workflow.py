#!/usr/bin/env python3
"""
Test script to simulate the exact agent workflow and identify the issue.
"""

import os
import sys
import json
import asyncio
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def simulate_agent_workflow():
    """Simulate the exact workflow the agent follows."""
    print("🤖 Simulating agent workflow...")
    
    try:
        # Step 1: Read current query generator code
        print("\n1. Reading query generator code...")
        from tools.code_modification_tool import _find_query_generator_file
        
        file_path = _find_query_generator_file()
        if not file_path:
            print("❌ Could not find query generator file")
            return False
        
        with open(file_path, 'r') as f:
            current_code = f.read()
        
        print(f"✅ Read {len(current_code)} characters from query generator")
        
        # Step 2: Create mock fuzzing results (no bugs found scenario)
        print("\n2. Creating mock fuzzing results...")
        mock_fuzzing_results = {
            "execution_summary": {
                "queries_executed": 5,
                "successful_queries": 4,
                "expected_errors": 1,
                "actual_bugs": 0,
                "crashes_found": 0,
                "server_errors_found": 0,
                "timeouts_found": 0
            },
            "detailed_logs": [
                {
                    "query": "SELECT * FROM users WHERE id = 1",
                    "success": True,
                    "error_type": None
                },
                {
                    "query": "SELECT COUNT(*) FROM products WHERE price > 100",
                    "success": False,
                    "error_type": "schema_error"
                }
            ]
        }
        
        fuzzing_results_json = json.dumps(mock_fuzzing_results)
        print(f"✅ Created mock fuzzing results: {len(fuzzing_results_json)} characters")
        
        # Step 3: Analyze and get improvement suggestions
        print("\n3. Analyzing and getting improvement suggestions...")
        from tools.query_generator_improvement_tool import _generate_improvement_suggestions
        
        # Calculate metrics like the real function does
        execution_summary = mock_fuzzing_results["execution_summary"]
        queries_executed = execution_summary["queries_executed"]
        successful_queries = execution_summary["successful_queries"]
        expected_errors = execution_summary["expected_errors"]
        actual_bugs = execution_summary["actual_bugs"]
        
        success_rate = (successful_queries / queries_executed * 100) if queries_executed > 0 else 0
        error_rate = (expected_errors / queries_executed * 100) if queries_executed > 0 else 0
        bug_rate = (actual_bugs / queries_executed * 100) if queries_executed > 0 else 0
        
        print(f"   Success rate: {success_rate:.1f}%")
        print(f"   Error rate: {error_rate:.1f}%")
        print(f"   Bug rate: {bug_rate:.1f}%")
        
        # Generate suggestions
        suggestions = _generate_improvement_suggestions(success_rate, error_rate, bug_rate, 1)
        
        # Create the full analysis result like the real function does
        analysis_result = {
            "analysis": f"Iteration 1: Success rate of {success_rate:.1f}% suggests queries may not be aggressive enough. Found {actual_bugs} bugs out of {queries_executed} queries.",
            "success_rate_assessment": "too_high" if success_rate > 50 else "appropriate" if success_rate > 20 else "too_low",
            "suggested_modifications": suggestions,
            "expected_impact": f"These changes should reduce success rate to 20-40% and increase bug detection probability by making queries more complex and edge-case prone."
        }
        
        improvement_suggestions_json = json.dumps(analysis_result, indent=2)
        print(f"✅ Generated improvement suggestions: {len(suggestions)} modifications")
        for i, suggestion in enumerate(suggestions):
            print(f"   {i+1}. {suggestion['description']}")
        
        # Step 4: Apply improvements
        print("\n4. Applying improvements...")
        
        # Simulate the apply_query_generator_improvements function
        try:
            # Parse the improvement suggestions (like the real function does)
            parsed_suggestions = json.loads(improvement_suggestions_json)
            
            if "error" in parsed_suggestions:
                print(f"❌ Cannot apply improvements due to analysis error: {parsed_suggestions['error']}")
                return False
            
            # Read the current file content
            with open(file_path, 'r') as f:
                original_content = f.read()
            
            modified_content = original_content
            modifications_applied = 0
            modification_details = []
            
            # Apply each suggested modification
            suggested_mods = parsed_suggestions.get("suggested_modifications", [])
            print(f"   Processing {len(suggested_mods)} suggested modifications...")
            
            for i, mod in enumerate(suggested_mods):
                location = mod.get("location", "")
                change_type = mod.get("change_type", "")
                description = mod.get("description", "")
                code_snippet = mod.get("code_snippet", "")
                
                print(f"   Modification {i+1}:")
                print(f"     Location: {location}")
                print(f"     Type: {change_type}")
                print(f"     Description: {description}")
                print(f"     Code snippet: {code_snippet[:50]}...")
                
                try:
                    print(f"     Checking condition: change_type='{change_type}', location='{location}'")
                    print(f"     safe_query_probability in location: {'safe_query_probability' in location}")
                    print(f"     safe_query_probability in description: {'safe_query_probability' in description}")

                    if change_type == "modify" and ("safe_query_probability" in location or "safe_query_probability" in description):
                        # Test the regex pattern
                        import re
                        pattern = r'self\.safe_query_probability\s*=\s*[\d.]+.*?(?=\s*#|$)'
                        matches = re.findall(pattern, modified_content)
                        print(f"     Found {len(matches)} matches for pattern")

                        if re.search(pattern, modified_content):
                            modified_content = re.sub(pattern, code_snippet.strip(), modified_content)
                            modifications_applied += 1
                            modification_details.append(f"Modified safe_query_probability: {description}")
                            print(f"     ✅ Applied modification successfully")
                        else:
                            print(f"     ❌ Pattern not found in content")
                            modification_details.append(f"Failed to find pattern for: {description}")
                    else:
                        print(f"     ⚠️ Condition not met for modification")
                        print(f"       Expected: change_type='modify' AND ('safe_query_probability' in location OR description)")
                        print(f"       Actual: change_type='{change_type}', location='{location}', description='{description}'")
                        modification_details.append(f"Condition not met for: {description}")
                        
                except Exception as e:
                    print(f"     ❌ Error applying modification: {e}")
                    modification_details.append(f"Failed to apply modification '{description}': {str(e)}")
            
            # Report results
            if modifications_applied > 0:
                print(f"✅ Successfully applied {modifications_applied} modifications")
                print("   Details:")
                for detail in modification_details:
                    print(f"     - {detail}")
                
                # Don't actually write the file in test mode
                print("   (File not modified in test mode)")
                return True
            else:
                print(f"❌ No modifications could be applied")
                print("   Details:")
                for detail in modification_details:
                    print(f"     - {detail}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            return False
        except Exception as e:
            print(f"❌ Error applying improvements: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Error in workflow simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("=" * 70)
    print("AGENT WORKFLOW SIMULATION TEST")
    print("=" * 70)
    
    success = simulate_agent_workflow()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ AGENT WORKFLOW SIMULATION PASSED")
        print("The improvement process should work correctly.")
        return 0
    else:
        print("❌ AGENT WORKFLOW SIMULATION FAILED")
        print("There may be an issue with the improvement application logic.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
