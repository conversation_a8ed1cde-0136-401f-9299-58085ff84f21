2025-06-13T18:06:42.885445Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T18:06:44.651165Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:06:44.652766Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=2 ms status=200
2025-06-13T18:06:44.656950Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:06:44.719383Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=62 ms status=200
2025-06-13T18:06:44.721861Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:06:44.819426Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=97 ms status=200
2025-06-13T18:06:45.918692Z  WARN embucketd: SIGTERM received, starting graceful shutdown
2025-06-13T18:06:45.918749Z  WARN embucketd: signal received, starting graceful shutdown
