2025-06-13T17:35:47.809254Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T17:35:49.770218Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:49.770551Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:49.773739Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:49.796537Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=22 ms status=200
2025-06-13T17:35:49.800075Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:49.894685Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=94 ms status=200
2025-06-13T17:35:49.900267Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=cddcfdfa-b3c9-46d4-afdf-0dd03ed4640f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:49.901106Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=cddcfdfa-b3c9-46d4-afdf-0dd03ed4640f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:49.906404Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6ad5b7b1-d1fb-4e18-8193-6c68d1494e53 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.094725Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6ad5b7b1-d1fb-4e18-8193-6c68d1494e53 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=188 ms status=200
2025-06-13T17:35:50.100596Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ddbdda3c-981e-4922-a564-fb48f855b957&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.100978Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ddbdda3c-981e-4922-a564-fb48f855b957&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:50.104623Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=971df4ea-fc43-4f32-8095-3a04c416cb3b version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.294939Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=971df4ea-fc43-4f32-8095-3a04c416cb3b version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:50.300313Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=67bc6936-ba0a-4422-bfd9-8efe7d34fce9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.300774Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=67bc6936-ba0a-4422-bfd9-8efe7d34fce9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:50.304602Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=52e3d00b-653d-49f6-8a52-10bac6ad41fd version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.494955Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=52e3d00b-653d-49f6-8a52-10bac6ad41fd version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:50.499412Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e10a75d3-1f85-4386-aa11-417e8def7547&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.499786Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e10a75d3-1f85-4386-aa11-417e8def7547&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:50.503306Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=42f4e565-3b3a-46f8-b2eb-dce4bb9c290a version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.695688Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=42f4e565-3b3a-46f8-b2eb-dce4bb9c290a version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:35:50.700317Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=04e2d8d1-dc2b-4ca7-8c1d-344ba177ea16&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.700700Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=04e2d8d1-dc2b-4ca7-8c1d-344ba177ea16&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:50.705567Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9d0f0b29-acfe-41f9-9d85-f90a541fe93f version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.894696Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9d0f0b29-acfe-41f9-9d85-f90a541fe93f version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:35:50.899997Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d902da72-715b-47ee-a9ef-184a7056d1e2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:50.900440Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d902da72-715b-47ee-a9ef-184a7056d1e2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:50.904375Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=58530ec5-4928-45d1-89ab-75ada50ddf44 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.095875Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=58530ec5-4928-45d1-89ab-75ada50ddf44 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:35:51.101102Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=48405922-f6f5-4955-89ea-8769f807eb13&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.101540Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=48405922-f6f5-4955-89ea-8769f807eb13&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:51.105236Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=33fff247-ea29-44c1-b112-435972f1a755 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.295674Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=33fff247-ea29-44c1-b112-435972f1a755 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:51.300793Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=80bcd3af-8086-4667-94ae-9a6c602f1514&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.301212Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=80bcd3af-8086-4667-94ae-9a6c602f1514&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:51.304991Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=536b83e7-7850-400d-b610-07473836cddd version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.494102Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=536b83e7-7850-400d-b610-07473836cddd version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:35:51.499784Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=fae9b84a-c827-4d26-9833-ea3763d3ef7a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.500234Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=fae9b84a-c827-4d26-9833-ea3763d3ef7a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:51.504014Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3283a63c-1e44-4123-8fb7-d6866340b89c version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.694411Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3283a63c-1e44-4123-8fb7-d6866340b89c version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:51.699439Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e6f94d8b-b53d-4613-9099-387b002c498a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.699830Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e6f94d8b-b53d-4613-9099-387b002c498a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:51.703589Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fc0e5e1f-67e7-4c86-b948-1a7347627e57 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.894496Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fc0e5e1f-67e7-4c86-b948-1a7347627e57 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:51.899321Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3130a660-0c70-4074-a3cf-0ec582e41d3f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:51.899738Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3130a660-0c70-4074-a3cf-0ec582e41d3f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:51.903919Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=532e7465-f7a4-494d-b80f-704eef314395 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.095383Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=532e7465-f7a4-494d-b80f-704eef314395 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:35:52.100737Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=656e2ded-5003-47b6-8735-00bf97444638&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.101210Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=656e2ded-5003-47b6-8735-00bf97444638&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:52.105098Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f89ad46d-13e4-4eef-89a5-06ce600c6ef2 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.295500Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f89ad46d-13e4-4eef-89a5-06ce600c6ef2 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:52.300609Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5c53cf05-bbf5-4955-92f4-2a77619f6ff2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.301020Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5c53cf05-bbf5-4955-92f4-2a77619f6ff2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:52.304712Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=43f015a6-e212-4b90-9503-5811129d6d44 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.494375Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=43f015a6-e212-4b90-9503-5811129d6d44 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:35:52.498786Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=624a7d07-1b17-4ec9-802f-2e9df4f572ca&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.499181Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=624a7d07-1b17-4ec9-802f-2e9df4f572ca&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:52.502648Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=16aa6745-49f0-4e55-9d88-9c757ccfbbbf version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.694496Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=16aa6745-49f0-4e55-9d88-9c757ccfbbbf version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:35:52.699601Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0f166d61-771b-41a2-84cd-269ef82202f3&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.700029Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0f166d61-771b-41a2-84cd-269ef82202f3&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:52.703842Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=05acfc79-375b-47b0-a4cc-d7f06f8b0328 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.895109Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=05acfc79-375b-47b0-a4cc-d7f06f8b0328 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:35:52.899643Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6ede5054-0b99-4d38-bddc-5b51aa87e0c2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:52.900078Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6ede5054-0b99-4d38-bddc-5b51aa87e0c2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:52.903957Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c237edbf-c4d7-4be4-991d-88b97d7d3d25 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.094628Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c237edbf-c4d7-4be4-991d-88b97d7d3d25 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:53.098809Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3b3e9092-f3f2-46b0-9714-87125e349880&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.099227Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3b3e9092-f3f2-46b0-9714-87125e349880&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:53.102902Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=72c366d8-143f-4221-93de-fc5aafdb7e8e version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.295225Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=72c366d8-143f-4221-93de-fc5aafdb7e8e version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:35:53.300563Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8d33ba20-687f-415e-bf3f-dd5db393aef7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.301065Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8d33ba20-687f-415e-bf3f-dd5db393aef7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:53.304753Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3f95bfa3-eed2-42c3-88b2-ba2af64d6c6f version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.494659Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3f95bfa3-eed2-42c3-88b2-ba2af64d6c6f version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:35:53.499559Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=04d3992d-f315-44fb-91ce-9acd295fdd44&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.499943Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=04d3992d-f315-44fb-91ce-9acd295fdd44&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:53.504812Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=35005dc8-8381-4513-910f-ca6bf7394acf version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.694536Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=35005dc8-8381-4513-910f-ca6bf7394acf version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:35:53.699469Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=640fc62e-76b2-4f5d-b37e-53b198bf69a8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.699877Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=640fc62e-76b2-4f5d-b37e-53b198bf69a8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:53.703667Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c692e7a9-cde0-4af5-acd8-6f617c8bb9ca version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.894401Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c692e7a9-cde0-4af5-acd8-6f617c8bb9ca version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:53.899101Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d78d42a2-352f-4e1b-bdd1-60a25791a5a4&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:53.899486Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d78d42a2-352f-4e1b-bdd1-60a25791a5a4&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:53.903137Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=b731136d-8206-4ede-afc5-b19f251fd6a4 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.095482Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=b731136d-8206-4ede-afc5-b19f251fd6a4 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:35:54.100558Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b78090e9-b801-4497-8b3d-22cac8c45d08&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.101037Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b78090e9-b801-4497-8b3d-22cac8c45d08&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:54.104685Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ad5c6b11-840c-410a-a332-7ff6df5285c1 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.295076Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ad5c6b11-840c-410a-a332-7ff6df5285c1 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:54.299926Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=31ca3418-bf7c-4041-b717-dc58ebb12806&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.300331Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=31ca3418-bf7c-4041-b717-dc58ebb12806&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:54.304364Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cda7646c-048a-474e-a3b4-7885d9a7513f version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.494914Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cda7646c-048a-474e-a3b4-7885d9a7513f version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:35:54.503744Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=656a1237-0ad0-45f4-b247-79b00ca3f088&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.504157Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=656a1237-0ad0-45f4-b247-79b00ca3f088&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:54.507682Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=32f44b8b-fa25-4a55-868b-f31dab903ed8 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.594955Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=32f44b8b-fa25-4a55-868b-f31dab903ed8 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=87 ms status=200
2025-06-13T17:35:54.710061Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=45834f11-e573-48ca-9752-e3c85513cedd&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.710492Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=45834f11-e573-48ca-9752-e3c85513cedd&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:54.714316Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6db4930b-3ed2-4319-a8c2-418c12370f24 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.794286Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6db4930b-3ed2-4319-a8c2-418c12370f24 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=79 ms status=200
2025-06-13T17:35:54.909516Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e61ceaa0-3da4-4e32-aee5-4b91a086ac8e&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.909964Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e61ceaa0-3da4-4e32-aee5-4b91a086ac8e&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:54.913925Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9554b763-2a0b-472c-aef1-4c71e30ccfa9 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:54.994392Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9554b763-2a0b-472c-aef1-4c71e30ccfa9 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=80 ms status=200
2025-06-13T17:35:55.108657Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b2d878d5-f7f6-4c7b-ac1f-699fda290cc8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.109095Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b2d878d5-f7f6-4c7b-ac1f-699fda290cc8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:55.112762Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=788ddc0d-6e87-4b6a-98ac-f0dc425f9b41 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.194024Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=788ddc0d-6e87-4b6a-98ac-f0dc425f9b41 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:35:55.308332Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=7b3ea463-3626-486d-a4bf-b20376a74798&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.308747Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=7b3ea463-3626-486d-a4bf-b20376a74798&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:55.312407Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ad7a8cd-1773-42d6-8d23-11b598a655b2 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.396511Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ad7a8cd-1773-42d6-8d23-11b598a655b2 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=84 ms status=200
2025-06-13T17:35:55.507468Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3f4b78f5-0e81-467b-92b2-27558374550c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.507831Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3f4b78f5-0e81-467b-92b2-27558374550c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:55.511167Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ea52a52a-8ad8-4fe5-9461-8c518db1f5cc version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.594951Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ea52a52a-8ad8-4fe5-9461-8c518db1f5cc version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=83 ms status=200
2025-06-13T17:35:55.708494Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b06e1a87-acfc-4067-92b7-cf27276c5de9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.708945Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b06e1a87-acfc-4067-92b7-cf27276c5de9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:55.712650Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7c58edfa-9346-458c-94ad-50637c553f60 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.794892Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7c58edfa-9346-458c-94ad-50637c553f60 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:35:55.908430Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=753708ff-1f49-443d-8042-132573e3d3aa&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.908846Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=753708ff-1f49-443d-8042-132573e3d3aa&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:55.912615Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=49295481-52ea-4f36-8ecc-946e842e1d73 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:55.995455Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=49295481-52ea-4f36-8ecc-946e842e1d73 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:35:56.108515Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e9ac0efe-7092-4220-95a4-ef745820afce&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:56.108920Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e9ac0efe-7092-4220-95a4-ef745820afce&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:56.112679Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3b27b708-7abd-47dc-a4c2-5b9de155540d version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:56.195029Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3b27b708-7abd-47dc-a4c2-5b9de155540d version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:35:56.309602Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8eb4f5fb-f86f-45c3-a812-28d4342be832&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:56.310034Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8eb4f5fb-f86f-45c3-a812-28d4342be832&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:35:56.314078Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=4f127e8d-8236-4c89-a184-679ed7ea5ea4 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:35:56.394438Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=4f127e8d-8236-4c89-a184-679ed7ea5ea4 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=80 ms status=200
2025-06-13T17:35:56.501668Z  WARN embucketd: SIGTERM received, starting graceful shutdown
2025-06-13T17:35:56.501736Z  WARN embucketd: signal received, starting graceful shutdown
