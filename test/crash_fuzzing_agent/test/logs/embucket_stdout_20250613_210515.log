2025-06-13T18:05:15.365863Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T18:05:17.194234Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:05:17.195461Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=2 ms status=200
2025-06-13T18:05:17.199734Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:05:17.208894Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=9 ms status=200
2025-06-13T18:05:17.211538Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:05:17.307774Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=96 ms status=200
2025-06-13T18:05:18.429309Z  WARN embucketd: SIGTERM received, starting graceful shutdown
2025-06-13T18:05:18.429362Z  WARN embucketd: signal received, starting graceful shutdown
