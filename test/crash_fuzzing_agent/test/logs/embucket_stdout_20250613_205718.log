2025-06-13T17:57:18.957791Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T17:57:20.775765Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:20.777298Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=1 ms status=200
2025-06-13T17:57:20.784631Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:20.892188Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=107 ms status=200
2025-06-13T17:57:20.897534Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:20.989289Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=91 ms status=200
2025-06-13T17:57:20.997450Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9ac3c829-c56c-4670-afff-05f62b5842c8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.004639Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9ac3c829-c56c-4670-afff-05f62b5842c8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=7 ms status=200
2025-06-13T17:57:21.013682Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fa244b1d-40d9-40d3-9982-d0e26f692596 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.190780Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fa244b1d-40d9-40d3-9982-d0e26f692596 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=177 ms status=200
2025-06-13T17:57:21.206519Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=270ea6d5-f481-4017-9198-e4293815e504&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.206915Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=270ea6d5-f481-4017-9198-e4293815e504&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:21.210971Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f9f23028-539a-42fa-b2db-fcea3810d452 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.389346Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f9f23028-539a-42fa-b2db-fcea3810d452 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=178 ms status=200
2025-06-13T17:57:21.392930Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=fb2759c0-4a39-44e1-b18d-eeb6b35dbc72&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.393433Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=fb2759c0-4a39-44e1-b18d-eeb6b35dbc72&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:21.397150Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=2d14046f-3b04-4aa5-a453-e6c735f5d017 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.590805Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=2d14046f-3b04-4aa5-a453-e6c735f5d017 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=193 ms status=200
2025-06-13T17:57:21.596746Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=89b33f8a-6a38-4962-8caa-4aa97eb6cffb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.597199Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=89b33f8a-6a38-4962-8caa-4aa97eb6cffb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:21.600811Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fb97cb9d-1adb-4dd9-83ea-4d2e59d05ba0 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.790646Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fb97cb9d-1adb-4dd9-83ea-4d2e59d05ba0 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:57:21.796187Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=943a6814-53bd-47ee-81b3-7a7685191311&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.796652Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=943a6814-53bd-47ee-81b3-7a7685191311&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:21.800617Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9a7b6e6a-ba9b-44fc-921f-f666b17d2376 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.989986Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9a7b6e6a-ba9b-44fc-921f-f666b17d2376 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:57:21.995144Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=dbdbda97-690c-48d8-8d65-fe1828790f89&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:21.995509Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=dbdbda97-690c-48d8-8d65-fe1828790f89&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:21.998758Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=4a48c3ed-117d-41a4-a031-4572be23ad77 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.189400Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=4a48c3ed-117d-41a4-a031-4572be23ad77 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:57:22.192849Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=36aec328-0569-4745-89ad-38874d90a306&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.193249Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=36aec328-0569-4745-89ad-38874d90a306&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:22.196521Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=36d4f366-5830-4ffd-a521-af819da8c914 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.389756Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=36d4f366-5830-4ffd-a521-af819da8c914 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=193 ms status=200
2025-06-13T17:57:22.393154Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b2ae0058-233c-4b9f-bffd-914bee344ba7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.393480Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b2ae0058-233c-4b9f-bffd-914bee344ba7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:22.396308Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=75fab716-04e2-416d-bcf7-3effb2917801 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.589630Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=75fab716-04e2-416d-bcf7-3effb2917801 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=193 ms status=200
2025-06-13T17:57:22.593248Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5e8ef699-4148-4572-9116-d9a2b67ee78e&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.593595Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5e8ef699-4148-4572-9116-d9a2b67ee78e&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:22.597201Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3ce257b0-99d9-49ea-a824-fdaa1e4ae380 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.789730Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3ce257b0-99d9-49ea-a824-fdaa1e4ae380 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:57:22.794436Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e8f77440-f888-45c3-88ae-06f42221c5f2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.794833Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e8f77440-f888-45c3-88ae-06f42221c5f2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:22.798511Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c4f90ae7-ad54-419e-b606-8549313646b1 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.990337Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c4f90ae7-ad54-419e-b606-8549313646b1 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:57:22.994042Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9ea27e39-1983-47da-ba24-7b35ec09af9d&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:22.994653Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9ea27e39-1983-47da-ba24-7b35ec09af9d&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:22.998460Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3a4ae360-cddb-4c75-8558-f37fdfd52f68 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.190444Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3a4ae360-cddb-4c75-8558-f37fdfd52f68 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:57:23.196674Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=107c6773-c6e3-4697-a4c6-1c2aec90d6ae&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.197345Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=107c6773-c6e3-4697-a4c6-1c2aec90d6ae&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:23.201202Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d555cad2-8b8a-4207-9d79-9db4a6c8e8b7 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.390807Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d555cad2-8b8a-4207-9d79-9db4a6c8e8b7 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:57:23.396658Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=592a7f38-2cef-43bd-9469-5c04aaf573cf&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.397074Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=592a7f38-2cef-43bd-9469-5c04aaf573cf&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:23.400586Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=bf091ece-ecf9-4008-9b84-4604c5a32c26 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.590757Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=bf091ece-ecf9-4008-9b84-4604c5a32c26 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:57:23.597788Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5c05758b-0d39-44fa-85ff-84c24d1d1022&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.598184Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5c05758b-0d39-44fa-85ff-84c24d1d1022&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:23.601838Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ac2c871e-ccdc-446b-b337-79b062ae5d62 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.790728Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ac2c871e-ccdc-446b-b337-79b062ae5d62 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=188 ms status=200
2025-06-13T17:57:23.795276Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2b42203c-ff0b-4392-bfef-4e9bb6f1e044&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.795764Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2b42203c-ff0b-4392-bfef-4e9bb6f1e044&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:23.800171Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f80f95aa-44fc-4c44-ac9b-20695dbc6552 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.989916Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f80f95aa-44fc-4c44-ac9b-20695dbc6552 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:57:23.995800Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=30ddc085-7e96-408e-b04b-a8b95fc940b7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:23.996468Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=30ddc085-7e96-408e-b04b-a8b95fc940b7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:24.000149Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=896f1769-190d-4f6b-b055-a0fc173435a1 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.190749Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=896f1769-190d-4f6b-b055-a0fc173435a1 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:57:24.196627Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=86139a32-b567-40dc-a259-b39f3e7707ac&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.197043Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=86139a32-b567-40dc-a259-b39f3e7707ac&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:24.201491Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=42a1fc8b-97f9-4610-9cf9-10c3d515911d version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.389853Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=42a1fc8b-97f9-4610-9cf9-10c3d515911d version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=188 ms status=200
2025-06-13T17:57:24.393718Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=f2a4f6a7-6bb5-4bd3-86ee-9a044d857140&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.394071Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=f2a4f6a7-6bb5-4bd3-86ee-9a044d857140&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:24.397436Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=76109623-ca24-459a-89b4-1f80292ce66b version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.791896Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=76109623-ca24-459a-89b4-1f80292ce66b version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=394 ms status=200
2025-06-13T17:57:24.796677Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0a8fe9ce-17c0-4c07-85b0-f78c10984614&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.797107Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0a8fe9ce-17c0-4c07-85b0-f78c10984614&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:24.800651Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=006dd455-648d-4c95-ba30-49974e3351b5 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.990196Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=006dd455-648d-4c95-ba30-49974e3351b5 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:57:24.994880Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=86240265-780d-4751-a7e5-f92f8dcdc169&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:24.995271Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=86240265-780d-4751-a7e5-f92f8dcdc169&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:24.998715Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ada4627-46aa-440b-9ca3-8b55e11b9689 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.189376Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ada4627-46aa-440b-9ca3-8b55e11b9689 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:57:25.193231Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4c8d4cda-fc8e-47ba-b1dc-0e589d435578&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.193571Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4c8d4cda-fc8e-47ba-b1dc-0e589d435578&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:25.197491Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=a597d111-60e6-495c-b5db-2245b46bb3ed version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.389944Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=a597d111-60e6-495c-b5db-2245b46bb3ed version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:57:25.393522Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ac7e8858-b099-4af3-919b-1baa084ec57b&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.393877Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ac7e8858-b099-4af3-919b-1baa084ec57b&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:25.397253Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c08d60a0-5f79-4e50-bc60-438824ef8dc3 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.590418Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c08d60a0-5f79-4e50-bc60-438824ef8dc3 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=193 ms status=200
2025-06-13T17:57:25.594629Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=669ec273-435c-479c-a582-b5710988651c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.595041Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=669ec273-435c-479c-a582-b5710988651c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:25.598623Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=b26007e4-2cc0-433f-ab04-e3132b698b17 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.790308Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=b26007e4-2cc0-433f-ab04-e3132b698b17 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:57:25.826557Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2279cd84-2f99-4423-9b44-b7b7cc219549&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.826977Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2279cd84-2f99-4423-9b44-b7b7cc219549&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:25.830647Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=8fb245f1-efde-46c8-80bc-b109df47f9b6 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:25.894243Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=8fb245f1-efde-46c8-80bc-b109df47f9b6 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=63 ms status=200
2025-06-13T17:57:26.009685Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=156d1d3d-e02d-499d-9037-d0d015f39fdb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.010259Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=156d1d3d-e02d-499d-9037-d0d015f39fdb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:26.014247Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d62ef1b5-5eb7-497b-9433-2a855c19a1a0 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.090193Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d62ef1b5-5eb7-497b-9433-2a855c19a1a0 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=75 ms status=200
2025-06-13T17:57:26.202558Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0bf42d54-04a0-4c7d-9ce2-38928b311482&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.202983Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0bf42d54-04a0-4c7d-9ce2-38928b311482&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:26.206664Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9a3342c4-04c2-4d6f-a3e0-35a58a012242 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.390790Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9a3342c4-04c2-4d6f-a3e0-35a58a012242 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=184 ms status=200
2025-06-13T17:57:26.504592Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e6b268b9-52fb-430d-bd06-9bde2e77287c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.504993Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e6b268b9-52fb-430d-bd06-9bde2e77287c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:26.508574Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ac5933e9-2d65-48ee-b755-301ff58c1a5c version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.590577Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ac5933e9-2d65-48ee-b755-301ff58c1a5c version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:57:26.702523Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=32af9a01-6b32-4057-ad01-7aadf14bdcbf&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.702924Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=32af9a01-6b32-4057-ad01-7aadf14bdcbf&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:26.706521Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=2c12a355-3d78-4606-949b-fdd477043cc2 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.790737Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=2c12a355-3d78-4606-949b-fdd477043cc2 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=84 ms status=200
2025-06-13T17:57:26.907853Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=16ce89fd-87fe-4015-a9b7-cc2c39bb1898&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.908285Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=16ce89fd-87fe-4015-a9b7-cc2c39bb1898&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:26.913007Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7d11a0e0-b274-4464-9daa-040d44d90b2d version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:26.990446Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7d11a0e0-b274-4464-9daa-040d44d90b2d version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=77 ms status=200
2025-06-13T17:57:27.103564Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e26e2465-d7b7-47cd-ae98-f647fc5aa04f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.104018Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e26e2465-d7b7-47cd-ae98-f647fc5aa04f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:27.108191Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0c9ef904-d01f-49cd-97b6-7e783a97a56b version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.189878Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0c9ef904-d01f-49cd-97b6-7e783a97a56b version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:57:27.304468Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4e93a221-7e00-4d73-b31a-4279babed496&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.304894Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4e93a221-7e00-4d73-b31a-4279babed496&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:27.308497Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=1f90487a-bd61-4a86-b274-38a690704623 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.390836Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=1f90487a-bd61-4a86-b274-38a690704623 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:57:27.505597Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e7094c5f-dcc6-4e2f-959b-bfdf60a559cb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.505997Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e7094c5f-dcc6-4e2f-959b-bfdf60a559cb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:27.509294Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fe943420-5589-489d-a24c-149b062086c8 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.590688Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fe943420-5589-489d-a24c-149b062086c8 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:57:27.704813Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9f901d23-2945-45a7-8580-a674421a3c88&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.705216Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9f901d23-2945-45a7-8580-a674421a3c88&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:57:27.708646Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ace8eaa4-3ded-4b14-aa76-6fcedfce63ac version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:57:27.790844Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ace8eaa4-3ded-4b14-aa76-6fcedfce63ac version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:57:27.914030Z  WARN embucketd: SIGTERM received, starting graceful shutdown
2025-06-13T17:57:27.914076Z  WARN embucketd: signal received, starting graceful shutdown
