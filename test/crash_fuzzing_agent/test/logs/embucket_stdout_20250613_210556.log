2025-06-13T18:05:56.746663Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T18:05:58.578063Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:05:58.579377Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=2 ms status=200
2025-06-13T18:05:58.583806Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:05:58.688094Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=104 ms status=200
2025-06-13T18:05:58.691888Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:05:58.787911Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=96 ms status=200
2025-06-13T18:05:59.884741Z  WARN embucketd: SIGTERM received, starting graceful shutdown
2025-06-13T18:05:59.884793Z  WARN embucketd: signal received, starting graceful shutdown
