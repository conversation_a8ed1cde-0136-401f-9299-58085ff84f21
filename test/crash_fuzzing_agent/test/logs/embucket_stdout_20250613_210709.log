2025-06-13T18:07:09.339645Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T18:07:11.298714Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:07:11.298989Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T18:07:11.301634Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:07:11.325990Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=24 ms status=200
2025-06-13T18:07:11.328710Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T18:07:11.424392Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=95 ms status=200
2025-06-13T18:07:12.550789Z  WARN embucketd: SIGTERM received, starting graceful shutdown
2025-06-13T18:07:12.550854Z  WARN embucketd: signal received, starting graceful shutdown
