#!/usr/bin/env python3
"""
Test script to simulate the entire improvement process and debug issues.
"""

import os
import sys
import json
import asyncio
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_improvement_analysis():
    """Test the improvement analysis function."""
    print("1. Testing improvement analysis...")
    
    # Import the function
    from tools.query_generator_improvement_tool import analyze_and_improve_query_generator
    
    # Create mock fuzzing results
    mock_results = {
        "execution_summary": {
            "queries_executed": 5,
            "successful_queries": 4,
            "expected_errors": 1,
            "actual_bugs": 0,
            "crashes_found": 0
        },
        "detailed_logs": [
            {
                "query": "SELECT * FROM users WHERE id = 1",
                "success": True,
                "error_type": None
            },
            {
                "query": "SELECT COUNT(*) FROM products",
                "success": True,
                "error_type": None
            }
        ]
    }
    
    # Mock current generator code (simplified)
    mock_code = """
class RandomQueryGenerator:
    def __init__(self):
        self.safe_query_probability = 0.5
    
    def generate_complex_query(self):
        return "SELECT * FROM users"
"""
    
    try:
        # This will fail because it's a FunctionTool, but let's see what happens
        # We need to call the underlying function directly
        from tools.query_generator_improvement_tool import _generate_improvement_suggestions
        
        # Test the suggestion generation
        suggestions = _generate_improvement_suggestions(80.0, 20.0, 0.0, 1)
        print(f"✅ Generated {len(suggestions)} improvement suggestions")
        for i, suggestion in enumerate(suggestions):
            print(f"   {i+1}. {suggestion['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in improvement analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_modification():
    """Test the file modification process."""
    print("\n2. Testing file modification...")
    
    try:
        # Import the functions
        from tools.code_modification_tool import _find_query_generator_file
        
        # Test file detection
        file_path = _find_query_generator_file()
        if not file_path:
            print("❌ Could not find query generator file")
            return False
        
        print(f"✅ Found query generator file: {file_path}")
        
        # Read the current file
        with open(file_path, 'r') as f:
            content = f.read()
        
        print(f"✅ File readable, length: {len(content)} characters")
        
        # Test a simple modification pattern
        import re
        pattern = r'self\.safe_query_probability\s*=\s*[\d.]+.*?(?=\s*#|$)'
        matches = re.findall(pattern, content)
        print(f"✅ Found {len(matches)} safe_query_probability patterns")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in file modification test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_improvement_cycle():
    """Test the full improvement cycle."""
    print("\n3. Testing full improvement cycle...")
    
    try:
        # Create a mock improvement suggestion
        mock_suggestions = {
            "analysis": "Test analysis",
            "suggested_modifications": [
                {
                    "location": "__init__ method",
                    "change_type": "modify",
                    "description": "Reduce safe_query_probability to make queries more aggressive",
                    "code_snippet": "self.safe_query_probability = 0.3  # Iteration 1: More aggressive"
                }
            ],
            "expected_impact": "Should make queries more aggressive"
        }
        
        # Test the apply function by calling the underlying implementation
        from tools.code_modification_tool import apply_query_generator_improvements
        
        # Since it's a FunctionTool, we need to test the logic differently
        # Let's test the pattern matching directly
        from tools.code_modification_tool import _find_query_generator_file
        
        file_path = _find_query_generator_file()
        if not file_path:
            print("❌ Could not find file for modification test")
            return False
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Test the regex pattern
        import re
        pattern = r'self\.safe_query_probability\s*=\s*[\d.]+.*?(?=\s*#|$)'
        matches = re.findall(pattern, content)
        
        if matches:
            print(f"✅ Pattern matching works: found {len(matches)} matches")
            print(f"   First match: {matches[0]}")
            
            # Test replacement
            new_content = re.sub(pattern, "self.safe_query_probability = 0.3  # Test modification", content)
            if new_content != content:
                print("✅ Pattern replacement works")
            else:
                print("❌ Pattern replacement failed - no changes made")
                
        else:
            print("❌ Pattern matching failed - no matches found")
            # Let's see what the actual content looks like around safe_query_probability
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'safe_query_probability' in line:
                    print(f"   Line {i+1}: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in full improvement cycle test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("=" * 70)
    print("ITERATIVE IMPROVEMENT PROCESS TEST")
    print("=" * 70)
    
    success1 = test_improvement_analysis()
    success2 = test_file_modification()
    success3 = test_full_improvement_cycle()
    
    print("\n" + "=" * 70)
    if success1 and success2 and success3:
        print("✅ ALL IMPROVEMENT PROCESS TESTS PASSED")
        return 0
    else:
        print("❌ SOME IMPROVEMENT PROCESS TESTS FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
